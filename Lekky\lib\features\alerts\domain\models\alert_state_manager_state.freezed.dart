// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'alert_state_manager_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AlertStateManagerState _$AlertStateManagerStateFromJson(
        Map<String, dynamic> json) =>
    _$$AlertStateManagerStateImplFromJson(json);

/// @nodoc
mixin _$AlertStateManagerState {
  /// Last low balance alert date
  DateTime? get lastLowBalanceAlertDate => throw _privateConstructorUsedError;

  /// Last time to top-up alert date
  DateTime? get lastTimeToTopUpAlertDate => throw _privateConstructorUsedError;

  /// Whether state is loading
  bool get isLoading => throw _privateConstructorUsedError;

  /// Error message if any
  String? get errorMessage => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AlertStateManagerStateCopyWith<AlertStateManagerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AlertStateManagerStateCopyWith<$Res> {
  factory $AlertStateManagerStateCopyWith(AlertStateManagerState value,
          $Res Function(AlertStateManagerState) then) =
      _$AlertStateManagerStateCopyWithImpl<$Res, AlertStateManagerState>;
  @useResult
  $Res call(
      {DateTime? lastLowBalanceAlertDate,
      DateTime? lastTimeToTopUpAlertDate,
      bool isLoading,
      String? errorMessage});
}

/// @nodoc
class _$AlertStateManagerStateCopyWithImpl<$Res,
        $Val extends AlertStateManagerState>
    implements $AlertStateManagerStateCopyWith<$Res> {
  _$AlertStateManagerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastLowBalanceAlertDate = freezed,
    Object? lastTimeToTopUpAlertDate = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      lastLowBalanceAlertDate: freezed == lastLowBalanceAlertDate
          ? _value.lastLowBalanceAlertDate
          : lastLowBalanceAlertDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastTimeToTopUpAlertDate: freezed == lastTimeToTopUpAlertDate
          ? _value.lastTimeToTopUpAlertDate
          : lastTimeToTopUpAlertDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AlertStateManagerStateImplCopyWith<$Res>
    implements $AlertStateManagerStateCopyWith<$Res> {
  factory _$$AlertStateManagerStateImplCopyWith(
          _$AlertStateManagerStateImpl value,
          $Res Function(_$AlertStateManagerStateImpl) then) =
      __$$AlertStateManagerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime? lastLowBalanceAlertDate,
      DateTime? lastTimeToTopUpAlertDate,
      bool isLoading,
      String? errorMessage});
}

/// @nodoc
class __$$AlertStateManagerStateImplCopyWithImpl<$Res>
    extends _$AlertStateManagerStateCopyWithImpl<$Res,
        _$AlertStateManagerStateImpl>
    implements _$$AlertStateManagerStateImplCopyWith<$Res> {
  __$$AlertStateManagerStateImplCopyWithImpl(
      _$AlertStateManagerStateImpl _value,
      $Res Function(_$AlertStateManagerStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? lastLowBalanceAlertDate = freezed,
    Object? lastTimeToTopUpAlertDate = freezed,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$AlertStateManagerStateImpl(
      lastLowBalanceAlertDate: freezed == lastLowBalanceAlertDate
          ? _value.lastLowBalanceAlertDate
          : lastLowBalanceAlertDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastTimeToTopUpAlertDate: freezed == lastTimeToTopUpAlertDate
          ? _value.lastTimeToTopUpAlertDate
          : lastTimeToTopUpAlertDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AlertStateManagerStateImpl implements _AlertStateManagerState {
  const _$AlertStateManagerStateImpl(
      {this.lastLowBalanceAlertDate,
      this.lastTimeToTopUpAlertDate,
      this.isLoading = false,
      this.errorMessage});

  factory _$AlertStateManagerStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$AlertStateManagerStateImplFromJson(json);

  /// Last low balance alert date
  @override
  final DateTime? lastLowBalanceAlertDate;

  /// Last time to top-up alert date
  @override
  final DateTime? lastTimeToTopUpAlertDate;

  /// Whether state is loading
  @override
  @JsonKey()
  final bool isLoading;

  /// Error message if any
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'AlertStateManagerState(lastLowBalanceAlertDate: $lastLowBalanceAlertDate, lastTimeToTopUpAlertDate: $lastTimeToTopUpAlertDate, isLoading: $isLoading, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AlertStateManagerStateImpl &&
            (identical(other.lastLowBalanceAlertDate, lastLowBalanceAlertDate) ||
                other.lastLowBalanceAlertDate == lastLowBalanceAlertDate) &&
            (identical(other.lastTimeToTopUpAlertDate, lastTimeToTopUpAlertDate) ||
                other.lastTimeToTopUpAlertDate == lastTimeToTopUpAlertDate) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, lastLowBalanceAlertDate,
      lastTimeToTopUpAlertDate, isLoading, errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AlertStateManagerStateImplCopyWith<_$AlertStateManagerStateImpl>
      get copyWith => __$$AlertStateManagerStateImplCopyWithImpl<
          _$AlertStateManagerStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AlertStateManagerStateImplToJson(
      this,
    );
  }
}

abstract class _AlertStateManagerState implements AlertStateManagerState {
  const factory _AlertStateManagerState(
      {final DateTime? lastLowBalanceAlertDate,
      final DateTime? lastTimeToTopUpAlertDate,
      final bool isLoading,
      final String? errorMessage}) = _$AlertStateManagerStateImpl;

  factory _AlertStateManagerState.fromJson(Map<String, dynamic> json) =
      _$AlertStateManagerStateImpl.fromJson;

  @override

  /// Last low balance alert date
  DateTime? get lastLowBalanceAlertDate;
  @override

  /// Last time to top-up alert date
  DateTime? get lastTimeToTopUpAlertDate;
  @override

  /// Whether state is loading
  bool get isLoading;
  @override

  /// Error message if any
  String? get errorMessage;
  @override
  @JsonKey(ignore: true)
  _$$AlertStateManagerStateImplCopyWith<_$AlertStateManagerStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
