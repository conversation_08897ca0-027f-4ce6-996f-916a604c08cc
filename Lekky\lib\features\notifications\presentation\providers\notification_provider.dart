import 'dart:async';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../core/di/service_locator.dart';
import '../../../../core/providers/error_provider.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/utils/event_bus.dart';
import '../../../../core/constants/preference_keys.dart';
import '../../domain/models/notification_state.dart';
import '../../domain/models/notification.dart';
import '../../domain/repositories/notification_repository.dart';
import '../../data/notification_service.dart';

part 'notification_provider.g.dart';

/// Notification repository provider
@riverpod
NotificationRepository notificationRepository(NotificationRepositoryRef ref) {
  return serviceLocator<NotificationRepository>();
}

/// Notification service provider
@riverpod
NotificationService notificationService(NotificationServiceRef ref) {
  return serviceLocator<NotificationService>();
}

/// Notification provider with comprehensive notification management
@riverpod
class Notification extends _$Notification {
  StreamSubscription<EventType>? _eventSubscription;

  @override
  Future<NotificationState> build() async {
    // Set up event listener for notification creation events
    _setupEventListener();

    return await _loadInitialState();
  }

  /// Set up event listener for notification creation events
  void _setupEventListener() {
    _eventSubscription?.cancel();
    _eventSubscription = EventBus().stream.listen((event) {
      if (event == EventType.notificationCreated) {
        Logger.info(
            'NotificationProvider: Received notificationCreated event, invalidating provider');
        // Invalidate provider for immediate UI update
        ref.invalidateSelf();
      }
    });
  }

  /// Load initial state
  Future<NotificationState> _loadInitialState() async {
    try {
      final initialState = NotificationState.initial();

      // Clean up old notifications (30-day retention)
      await _cleanupOldNotifications();

      await _loadNotifications(initialState);
      return state.value ?? initialState;
    } catch (error, stackTrace) {
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Notification initialization',
          );
      return NotificationState.initial().copyWith(
        errorMessage: 'Failed to load notifications: ${error.toString()}',
      );
    }
  }

  /// Create a low balance notification
  Future<void> createLowBalanceNotification(
      double balance, double threshold) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState.isNotificationTypeEnabled(NotificationType.lowBalance)) {
      return;
    }

    // Check deduplication - only send once per day
    if (!await _shouldSendNotification(NotificationType.lowBalance)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Low Balance Alert',
        message:
            'Your meter balance (£$balance) is below your alert threshold (£$threshold).',
        timestamp: DateTime.now(),
        type: NotificationType.lowBalance,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Track notification date for deduplication
      await _setLastNotificationDate(
          NotificationType.lowBalance, DateTime.now());

      // Invalidate provider for immediate UI update
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create low balance notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Low balance notification creation',
          );
    }
  }

  /// Create a time to top-up notification
  Future<void> createTimeToTopUpNotification(
      double daysRemaining, int daysInAdvance) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState.isNotificationTypeEnabled(NotificationType.timeToTopUp)) {
      return;
    }

    // Check deduplication - only send once per day
    if (!await _shouldSendNotification(NotificationType.timeToTopUp)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Time to Top-Up',
        message:
            'You have approximately ${daysRemaining.toStringAsFixed(1)} days of electricity remaining.',
        timestamp: DateTime.now(),
        type: NotificationType.timeToTopUp,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Track notification date for deduplication
      await _setLastNotificationDate(
          NotificationType.timeToTopUp, DateTime.now());

      // Invalidate provider for immediate UI update
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create time to top-up notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Time to top-up notification creation',
          );
    }
  }

  /// Create an invalid record notification
  Future<void> createInvalidRecordNotification(String details) async {
    final currentState = state.value ?? NotificationState.initial();

    if (!currentState
        .isNotificationTypeEnabled(NotificationType.invalidRecord)) {
      return;
    }

    try {
      final notification = AppNotification(
        title: 'Invalid Record Detected',
        message: 'An invalid record was detected: $details',
        timestamp: DateTime.now(),
        type: NotificationType.invalidRecord,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Invalidate provider for immediate UI update
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to create invalid record notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Invalid record notification creation',
          );
    }
  }

  /// Create a welcome notification for first-time users
  Future<void> createWelcomeNotification() async {
    try {
      final notification = AppNotification(
        title: 'Welcome to Lekky!',
        message:
            'Track your prepaid electricity meter readings and get alerts when it\'s time to top up. Tap the notification icon to see all notifications.',
        timestamp: DateTime.now(),
        type: NotificationType.welcome,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.showNotification(notification);

      // Invalidate provider for immediate UI update
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      Logger.error('Failed to create welcome notification: $error', stackTrace);
      // Don't show error to user for welcome notification
    }
  }

  /// Schedule a reading reminder notification
  Future<void> scheduleReadingReminder(DateTime scheduledDate) async {
    try {
      final notification = AppNotification(
        title: 'Meter Reading Reminder',
        message: 'It\'s time to take a new meter reading.',
        timestamp: scheduledDate,
        type: NotificationType.readingReminder,
      );

      final notificationService = ref.read(notificationServiceProvider);
      await notificationService.scheduleNotification(
          notification, scheduledDate);

      // Invalidate provider for immediate UI update
      ref.invalidateSelf();
    } catch (error, stackTrace) {
      Logger.error('Failed to schedule reading reminder: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Reading reminder scheduling',
          );
    }
  }

  /// Mark a notification as read
  Future<void> markAsRead(int id) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      await notificationRepo.markAsRead(id);

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications =
          currentState.notifications.map((notification) {
        return notification.id == id
            ? notification.copyWith(isRead: true)
            : notification;
      }).toList();

      final newUnreadCount =
          updatedNotifications.where((n) => !n.isRead).length;

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to mark notification as read: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Mark notification as read',
          );
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      await notificationRepo.markAllAsRead();

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications = currentState.notifications
          .map((notification) => notification.copyWith(isRead: true))
          .toList();

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: 0,
      ));
    } catch (error, stackTrace) {
      Logger.error(
          'Failed to mark all notifications as read: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Mark all notifications as read',
          );
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(int id) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final notificationService = ref.read(notificationServiceProvider);
      await notificationRepo.deleteNotification(id);
      await notificationService.cancelNotification(id);

      final currentState = state.value ?? NotificationState.initial();
      final updatedNotifications = currentState.notifications
          .where((notification) => notification.id != id)
          .toList();

      final newUnreadCount =
          updatedNotifications.where((n) => !n.isRead).length;

      state = AsyncValue.data(currentState.copyWith(
        notifications: updatedNotifications,
        unreadCount: newUnreadCount,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to delete notification: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Delete notification',
          );
    }
  }

  /// Delete all notifications - Fixed: Immediate UI update
  Future<void> deleteAllNotifications() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final notificationService = ref.read(notificationServiceProvider);
      await notificationRepo.deleteAllNotifications();
      await notificationService.cancelAllNotifications();

      // Immediate UI update
      final currentState = state.value ?? NotificationState.initial();
      state = AsyncValue.data(currentState.copyWith(
        notifications: [],
        unreadCount: 0,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to delete all notifications: $error', stackTrace);
      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Delete all notifications',
          );
    }
  }

  /// Refresh notifications
  Future<void> refresh() async {
    final currentState = state.value ?? NotificationState.initial();
    state = AsyncValue.data(currentState.copyWith(isLoading: true));
    await _loadNotifications(state.value!);
  }

  /// Clear error
  void clearError() {
    final currentState = state.value ?? NotificationState.initial();
    state = AsyncValue.data(currentState.copyWith(errorMessage: null));
  }

  /// Get last notification date for a specific type
  Future<DateTime?> _getLastNotificationDate(NotificationType type) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      final timestamp = prefs.getInt(key);
      return timestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : null;
    } catch (e) {
      Logger.error('Failed to get last notification date for $type: $e');
      return null;
    }
  }

  /// Set last notification date for a specific type
  Future<void> _setLastNotificationDate(
      NotificationType type, DateTime date) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = _getNotificationDateKey(type);
      await prefs.setInt(key, date.millisecondsSinceEpoch);
    } catch (e) {
      Logger.error('Failed to set last notification date for $type: $e');
    }
  }

  /// Check if notification should be sent (once per day limit)
  Future<bool> _shouldSendNotification(NotificationType type) async {
    final lastDate = await _getLastNotificationDate(type);
    if (lastDate == null) return true;

    final now = DateTime.now();

    // Check if it's a different calendar day (not just 24 hours)
    final isSameDay = lastDate.year == now.year &&
        lastDate.month == now.month &&
        lastDate.day == now.day;

    return !isSameDay;
  }

  /// Get preference key for notification date tracking
  String _getNotificationDateKey(NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return PreferenceKeys.lastLowBalanceNotificationDate;
      case NotificationType.timeToTopUp:
        return PreferenceKeys.lastTimeToTopUpNotificationDate;
      default:
        throw ArgumentError(
            'Unsupported notification type for deduplication: $type');
    }
  }

  /// Load notifications from repository
  Future<void> _loadNotifications(NotificationState currentState) async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);

      final notifications = await notificationRepo.getAllNotifications();
      final unreadCount = await notificationRepo.getUnreadCount();

      state = AsyncValue.data(currentState.copyWith(
        notifications: notifications,
        unreadCount: unreadCount,
        isLoading: false,
        errorMessage: null,
      ));
    } catch (error, stackTrace) {
      Logger.error('Failed to load notifications: $error', stackTrace);

      state = AsyncValue.data(currentState.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load notifications: ${error.toString()}',
      ));

      ref.read(globalErrorProvider.notifier).handleError(
            error,
            stackTrace,
            context: 'Notification loading',
          );
    }
  }

  /// Clean up notifications older than 30 days (best practice retention)
  Future<void> _cleanupOldNotifications() async {
    try {
      final notificationRepo = ref.read(notificationRepositoryProvider);
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      final allNotifications = await notificationRepo.getAllNotifications();
      final oldNotifications = allNotifications
          .where((notification) => notification.timestamp.isBefore(cutoffDate))
          .toList();

      if (oldNotifications.isNotEmpty) {
        for (final notification in oldNotifications) {
          if (notification.id != null) {
            await notificationRepo.deleteNotification(notification.id!);
          }
        }
        Logger.info('Cleaned up ${oldNotifications.length} old notifications');
      }
    } catch (error) {
      Logger.error('Failed to cleanup old notifications: $error');
      // Don't throw error as this is a background cleanup operation
    }
  }
}
