// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert_state_manager_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AlertStateManagerStateImpl _$$AlertStateManagerStateImplFromJson(
        Map<String, dynamic> json) =>
    _$AlertStateManagerStateImpl(
      lastLowBalanceAlertDate: json['lastLowBalanceAlertDate'] == null
          ? null
          : DateTime.parse(json['lastLowBalanceAlertDate'] as String),
      lastTimeToTopUpAlertDate: json['lastTimeToTopUpAlertDate'] == null
          ? null
          : DateTime.parse(json['lastTimeToTopUpAlertDate'] as String),
      isLoading: json['isLoading'] as bool? ?? false,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$$AlertStateManagerStateImplToJson(
        _$AlertStateManagerStateImpl instance) =>
    <String, dynamic>{
      'lastLowBalanceAlertDate':
          instance.lastLowBalanceAlertDate?.toIso8601String(),
      'lastTimeToTopUpAlertDate':
          instance.lastTimeToTopUpAlertDate?.toIso8601String(),
      'isLoading': instance.isLoading,
      'errorMessage': instance.errorMessage,
    };
