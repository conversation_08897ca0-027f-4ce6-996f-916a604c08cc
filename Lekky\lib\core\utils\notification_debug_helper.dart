import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../constants/preference_keys.dart';
import '../services/notification_permission_manager.dart';
import '../services/unified_alert_manager.dart';
import '../di/service_locator.dart';
import '../../features/notifications/data/notification_service.dart';
import '../../features/notifications/domain/models/notification.dart';
import '../../features/meter_readings/domain/repositories/meter_reading_repository.dart';
import 'logger.dart';

/// Debug utility for troubleshooting notification issues
class NotificationDebugHelper {
  static final NotificationDebugHelper _instance =
      NotificationDebugHelper._internal();
  factory NotificationDebugHelper() => _instance;
  NotificationDebugHelper._internal();

  /// Generate comprehensive notification system status report
  Future<Map<String, dynamic>> generateStatusReport() async {
    final report = <String, dynamic>{};

    try {
      // Check permissions
      report['permissions'] = await _checkPermissions();

      // Check settings
      report['settings'] = await _checkSettings();

      // Check service status
      report['services'] = await _checkServices();

      // Check recent notifications
      report['recent_notifications'] = await _checkRecentNotifications();

      // Check background flags
      report['background_flags'] = await _checkBackgroundFlags();

      // Check alert states
      report['alert_states'] = await _checkAlertStates();

      // System info
      report['system_info'] = await _getSystemInfo();

      report['timestamp'] = DateTime.now().toIso8601String();
      report['status'] = 'success';
    } catch (e) {
      report['error'] = e.toString();
      report['status'] = 'error';
      Logger.error(
          'NotificationDebugHelper: Error generating status report: $e');
    }

    return report;
  }

  /// Check notification permissions status
  Future<Map<String, dynamic>> _checkPermissions() async {
    final permissions = <String, dynamic>{};

    try {
      final permissionManager = NotificationPermissionManager();
      permissions['has_permission'] = await permissionManager.hasPermission();
      permissions['permission_check_success'] = true;
    } catch (e) {
      permissions['permission_check_success'] = false;
      permissions['permission_error'] = e.toString();
    }

    return permissions;
  }

  /// Check notification settings
  Future<Map<String, dynamic>> _checkSettings() async {
    final settings = <String, dynamic>{};

    try {
      final prefs = await SharedPreferences.getInstance();

      // Debug: Log all preference keys for troubleshooting
      Logger.info('NotificationDebugHelper: Checking preference keys:');
      Logger.info(
          '  Master notifications: ${(prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false) || (prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false) || (prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false)}');
      Logger.info(
          '  ${PreferenceKeys.lowBalanceAlertsEnabled}: ${prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled)}');
      Logger.info(
          '  ${PreferenceKeys.timeToTopUpAlertsEnabled}: ${prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled)}');

      // Check if any notification type is enabled
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      settings['notifications_enabled'] =
          lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;
      settings['low_balance_enabled'] =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      settings['time_to_top_up_enabled'] =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      settings['invalid_record_enabled'] =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      settings['reminders_enabled'] =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      settings['alert_threshold'] =
          prefs.getDouble(PreferenceKeys.alertThreshold) ?? 0.0;
      settings['days_in_advance'] =
          prefs.getInt(PreferenceKeys.daysInAdvance) ?? 0;

      // Check last notification dates
      settings['last_low_balance_notification'] =
          prefs.getString(PreferenceKeys.lastLowBalanceNotificationDate);
      settings['last_time_to_top_up_notification'] =
          prefs.getString(PreferenceKeys.lastTimeToTopUpNotificationDate);

      settings['settings_check_success'] = true;
    } catch (e) {
      settings['settings_check_success'] = false;
      settings['settings_error'] = e.toString();
    }

    return settings;
  }

  /// Check notification services status
  Future<Map<String, dynamic>> _checkServices() async {
    final services = <String, dynamic>{};

    try {
      // Check if notification service is registered
      try {
        await serviceLocator.getAsync<NotificationService>();
        services['notification_service_available'] = true;
      } catch (e) {
        services['notification_service_available'] = false;
        services['notification_service_error'] = e.toString();
      }

      // Check unified alert manager
      try {
        UnifiedAlertManager();
        services['alert_manager_available'] = true;
      } catch (e) {
        services['alert_manager_available'] = false;
        services['alert_manager_error'] = e.toString();
      }

      services['services_check_success'] = true;
    } catch (e) {
      services['services_check_success'] = false;
      services['services_error'] = e.toString();
    }

    return services;
  }

  /// Check recent notification activity
  Future<Map<String, dynamic>> _checkRecentNotifications() async {
    final notifications = <String, dynamic>{};

    try {
      final prefs = await SharedPreferences.getInstance();

      // Check for pending in-app alerts
      notifications['pending_in_app_alert'] = {
        'title': prefs.getString('pending_in_app_alert_title'),
        'message': prefs.getString('pending_in_app_alert_message'),
        'type': prefs.getString('pending_in_app_alert_type'),
        'timestamp': prefs.getString('pending_in_app_alert_timestamp'),
      };

      // Check scheduled notifications (if available)
      try {
        final plugin = FlutterLocalNotificationsPlugin();
        final pendingNotifications = await plugin.pendingNotificationRequests();
        notifications['pending_system_notifications'] =
            pendingNotifications.length;
        notifications['pending_notifications_details'] = pendingNotifications
            .map((n) => {
                  'id': n.id,
                  'title': n.title,
                  'body': n.body,
                })
            .toList();
      } catch (e) {
        notifications['pending_notifications_error'] = e.toString();
      }

      notifications['notifications_check_success'] = true;
    } catch (e) {
      notifications['notifications_check_success'] = false;
      notifications['notifications_error'] = e.toString();
    }

    return notifications;
  }

  /// Check background monitoring flags
  Future<Map<String, dynamic>> _checkBackgroundFlags() async {
    final flags = <String, dynamic>{};

    try {
      final prefs = await SharedPreferences.getInstance();

      flags['background_alert_check_requested'] =
          prefs.getBool('background_alert_check_requested') ?? false;
      flags['background_alert_check_time'] =
          prefs.getString('background_alert_check_time');

      // Calculate time since last background check
      final checkTimeString = prefs.getString('background_alert_check_time');
      if (checkTimeString != null) {
        try {
          final checkTime = DateTime.parse(checkTimeString);
          final hoursSinceCheck = DateTime.now().difference(checkTime).inHours;
          flags['hours_since_background_check'] = hoursSinceCheck;
        } catch (e) {
          flags['background_time_parse_error'] = e.toString();
        }
      }

      flags['background_flags_check_success'] = true;
    } catch (e) {
      flags['background_flags_check_success'] = false;
      flags['background_flags_error'] = e.toString();
    }

    return flags;
  }

  /// Check alert states and timing information
  Future<Map<String, dynamic>> _checkAlertStates() async {
    final alertStates = <String, dynamic>{};

    try {
      final prefs = await SharedPreferences.getInstance();
      final meterReadingRepo = serviceLocator<MeterReadingRepository>();
      final allReadings = await meterReadingRepo.getAllMeterReadings();

      // Alert state timing information
      final lastLowBalanceAlert =
          prefs.getString(PreferenceKeys.lastLowBalanceNotificationDate);
      final lastTimeToTopUpAlert =
          prefs.getString(PreferenceKeys.lastTimeToTopUpNotificationDate);

      alertStates['last_low_balance_alert'] = lastLowBalanceAlert;
      alertStates['last_time_to_top_up_alert'] = lastTimeToTopUpAlert;

      // Calculate if alerts can fire today
      if (lastLowBalanceAlert != null) {
        try {
          final lastAlert = DateTime.parse(lastLowBalanceAlert);
          final now = DateTime.now();
          final canFireToday = !(lastAlert.year == now.year &&
              lastAlert.month == now.month &&
              lastAlert.day == now.day);
          alertStates['low_balance_can_fire_today'] = canFireToday;
          alertStates['hours_since_low_balance_alert'] =
              now.difference(lastAlert).inHours;
        } catch (e) {
          alertStates['low_balance_date_parse_error'] = e.toString();
        }
      } else {
        alertStates['low_balance_can_fire_today'] = true;
      }

      if (lastTimeToTopUpAlert != null) {
        try {
          final lastAlert = DateTime.parse(lastTimeToTopUpAlert);
          final now = DateTime.now();
          final canFireToday = !(lastAlert.year == now.year &&
              lastAlert.month == now.month &&
              lastAlert.day == now.day);
          alertStates['time_to_top_up_can_fire_today'] = canFireToday;
          alertStates['hours_since_time_to_top_up_alert'] =
              now.difference(lastAlert).inHours;
        } catch (e) {
          alertStates['time_to_top_up_date_parse_error'] = e.toString();
        }
      } else {
        alertStates['time_to_top_up_can_fire_today'] = true;
      }

      // 3 AM reminder logic check
      final now = DateTime.now();
      final today3AM = DateTime(now.year, now.month, now.day, 3, 0, 0);
      final hasReadingAfter3AM =
          allReadings.any((reading) => reading.date.isAfter(today3AM));

      alertStates['reading_after_3am_today'] = hasReadingAfter3AM;
      alertStates['today_3am_cutoff'] = today3AM.toIso8601String();
      alertStates['reminder_would_be_skipped'] = hasReadingAfter3AM;

      // Recent readings count
      final last24Hours = now.subtract(const Duration(hours: 24));
      final recentReadingsCount = allReadings
          .where((reading) => reading.date.isAfter(last24Hours))
          .length;
      alertStates['readings_last_24_hours'] = recentReadingsCount;

      alertStates['alert_states_check_success'] = true;
    } catch (e) {
      alertStates['alert_states_check_success'] = false;
      alertStates['alert_states_error'] = e.toString();
    }

    return alertStates;
  }

  /// Get system information
  Future<Map<String, dynamic>> _getSystemInfo() async {
    final systemInfo = <String, dynamic>{};

    try {
      systemInfo['dart_version'] = '3.0+'; // Approximate
      systemInfo['flutter_version'] = '3.0+'; // Approximate
      systemInfo['timestamp'] = DateTime.now().toIso8601String();

      systemInfo['system_info_success'] = true;
    } catch (e) {
      systemInfo['system_info_success'] = false;
      systemInfo['system_info_error'] = e.toString();
    }

    return systemInfo;
  }

  /// Test notification system by firing a test notification
  Future<Map<String, dynamic>> testNotificationSystem() async {
    final testResult = <String, dynamic>{};

    try {
      Logger.info('NotificationDebugHelper: Starting notification system test');

      // Check permissions first
      final permissionManager = NotificationPermissionManager();
      final hasPermission = await permissionManager.hasPermission();

      testResult['has_permission'] = hasPermission;

      if (!hasPermission) {
        testResult['status'] = 'failed';
        testResult['reason'] = 'No notification permissions';
        return testResult;
      }

      // Try to fire a test notification
      try {
        final notificationService =
            await serviceLocator.getAsync<NotificationService>();

        // Create a test notification
        final testNotification = AppNotification(
          title: 'Lekky Test Notification',
          message:
              'This is a test notification to verify the system is working.',
          timestamp: DateTime.now(),
          type: NotificationType.welcome,
        );

        await notificationService.showNotification(testNotification);

        testResult['notification_fired'] = true;
        testResult['status'] = 'success';

        Logger.info(
            'NotificationDebugHelper: Test notification fired successfully');
      } catch (e) {
        testResult['notification_fired'] = false;
        testResult['notification_error'] = e.toString();
        testResult['status'] = 'failed';
        testResult['reason'] = 'Failed to fire test notification';

        Logger.error(
            'NotificationDebugHelper: Failed to fire test notification: $e');
      }
    } catch (e) {
      testResult['status'] = 'error';
      testResult['error'] = e.toString();
      Logger.error(
          'NotificationDebugHelper: Error in test notification system: $e');
    }

    testResult['timestamp'] = DateTime.now().toIso8601String();
    return testResult;
  }

  /// Clear all notification-related data (for debugging)
  Future<void> clearAllNotificationData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear notification settings
      // Note: notificationsEnabled preference key was removed
      await prefs.remove(PreferenceKeys.lowBalanceAlertsEnabled);
      await prefs.remove(PreferenceKeys.timeToTopUpAlertsEnabled);
      await prefs.remove(PreferenceKeys.invalidRecordAlertsEnabled);

      // Clear deduplication data
      await prefs.remove(PreferenceKeys.lastLowBalanceNotificationDate);
      await prefs.remove(PreferenceKeys.lastTimeToTopUpNotificationDate);

      // Clear background flags
      await prefs.remove('background_alert_check_requested');
      await prefs.remove('background_alert_check_time');

      // Clear in-app alert data
      await prefs.remove('pending_in_app_alert_title');
      await prefs.remove('pending_in_app_alert_message');
      await prefs.remove('pending_in_app_alert_type');
      await prefs.remove('pending_in_app_alert_timestamp');

      // Cancel all scheduled notifications
      try {
        final plugin = FlutterLocalNotificationsPlugin();
        await plugin.cancelAll();
      } catch (e) {
        Logger.error(
            'NotificationDebugHelper: Error canceling notifications: $e');
      }

      Logger.info('NotificationDebugHelper: Cleared all notification data');
    } catch (e) {
      Logger.error(
          'NotificationDebugHelper: Error clearing notification data: $e');
    }
  }

  /// Log comprehensive status report
  Future<void> logStatusReport() async {
    final report = await generateStatusReport();
    Logger.info('=== NOTIFICATION SYSTEM STATUS REPORT ===');
    Logger.info('Timestamp: ${report['timestamp']}');
    Logger.info('Status: ${report['status']}');

    if (report['permissions'] != null) {
      final permissions = report['permissions'] as Map<String, dynamic>;
      Logger.info('Permissions: ${permissions['has_permission']}');
    }

    if (report['settings'] != null) {
      final settings = report['settings'] as Map<String, dynamic>;
      Logger.info(
          'Notifications Enabled: ${settings['notifications_enabled']}');
      Logger.info('Low Balance Enabled: ${settings['low_balance_enabled']}');
      Logger.info(
          'Time to Top Up Enabled: ${settings['time_to_top_up_enabled']}');
    }

    if (report['services'] != null) {
      final services = report['services'] as Map<String, dynamic>;
      Logger.info(
          'Notification Service Available: ${services['notification_service_available']}');
      Logger.info(
          'Alert Manager Available: ${services['alert_manager_available']}');
    }

    if (report['alert_states'] != null) {
      final alertStates = report['alert_states'] as Map<String, dynamic>;
      Logger.info('=== ALERT STATES DEBUG ===');
      Logger.info(
          'Low Balance Can Fire Today: ${alertStates['low_balance_can_fire_today']}');
      Logger.info(
          'Time to Top-Up Can Fire Today: ${alertStates['time_to_top_up_can_fire_today']}');
      Logger.info(
          'Reading After 3 AM Today: ${alertStates['reading_after_3am_today']}');
      Logger.info(
          'Reminder Would Be Skipped: ${alertStates['reminder_would_be_skipped']}');
      Logger.info(
          'Readings Last 24 Hours: ${alertStates['readings_last_24_hours']}');

      if (alertStates['hours_since_low_balance_alert'] != null) {
        Logger.info(
            'Hours Since Low Balance Alert: ${alertStates['hours_since_low_balance_alert']}');
      }

      if (alertStates['hours_since_time_to_top_up_alert'] != null) {
        Logger.info(
            'Hours Since Time to Top-Up Alert: ${alertStates['hours_since_time_to_top_up_alert']}');
      }
    }

    Logger.info('=== END STATUS REPORT ===');
  }
}
