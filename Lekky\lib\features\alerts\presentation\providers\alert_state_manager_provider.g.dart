// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alert_state_manager_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$alertStateManagerHash() => r'8f7e4c5d9b2a1e6f3c8d7a9b4e5f2c1d';

/// Consolidated alert state management using Riverpod
///
/// Copied from [AlertStateManager].
@ProviderFor(AlertStateManager)
final alertStateManagerProvider = AutoDisposeAsyncNotifierProvider<
    AlertStateManager, AlertStateManagerState>.internal(
  AlertStateManager.new,
  name: r'alertStateManagerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$alertStateManagerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlertStateManager = AutoDisposeAsyncNotifier<AlertStateManagerState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member