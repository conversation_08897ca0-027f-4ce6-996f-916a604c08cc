import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/preference_keys.dart';
import '../utils/logger.dart';
import '../widgets/lekky_button.dart';
import 'notification_permission_manager.dart';
import 'background_monitoring_service.dart';

/// Helper service for managing notification settings and permissions
class NotificationSettingsHelper {
  static final NotificationSettingsHelper _instance =
      NotificationSettingsHelper._internal();

  factory NotificationSettingsHelper() => _instance;
  NotificationSettingsHelper._internal();

  /// Check if this is the first time any notification type is being enabled
  Future<bool> isFirstNotificationActivation() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;

      // Check if any notification type is currently enabled
      final anyEnabled =
          lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;

      return !anyEnabled;
    } catch (e) {
      Logger.error('Error checking first notification activation: $e');
      return false;
    }
  }

  /// Handle notification type activation with permission request
  Future<bool> handleNotificationActivation(
    BuildContext context,
    String notificationType,
  ) async {
    try {
      // Check if this is the first notification being enabled
      final isFirst = await isFirstNotificationActivation();

      if (isFirst) {
        // Request permissions for first-time activation
        final permissionManager = NotificationPermissionManager();
        final hasPermission = await permissionManager.hasPermission();

        if (!hasPermission) {
          final granted = await permissionManager.requestPermission(context);
          if (!granted) {
            // Show settings dialog if permission denied
            await permissionManager.showSettingsDialog(context);
            return false;
          }
        }

        // Initialize background monitoring for first-time setup
        try {
          final backgroundService = BackgroundMonitoringService();
          await backgroundService.updateMonitoring();
        } catch (e) {
          Logger.error('Failed to initialize background monitoring: $e');
          // Continue even if background monitoring fails
        }
      }

      return true;
    } catch (e) {
      Logger.error('Error handling notification activation: $e');
      return false;
    }
  }

  /// Check if notification permissions are available
  Future<bool> hasNotificationPermissions() async {
    try {
      final permissionManager = NotificationPermissionManager();
      return await permissionManager.hasPermission();
    } catch (e) {
      Logger.error('Error checking notification permissions: $e');
      return false;
    }
  }

  /// Show permission explanation dialog
  Future<void> showPermissionExplanation(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return _buildExplanationDialog(context);
      },
    );
  }

  /// Build explanation dialog with Lekky styling
  Widget _buildExplanationDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;
    final theme = Theme.of(context);

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: screenHeight * 0.1,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Fixed header
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.primary,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Notification Permissions',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: theme.colorScheme.onSurface),
                    onPressed: () => Navigator.of(context).pop(),
                    tooltip: 'Close',
                  ),
                ],
              ),
            ),
            // Scrollable content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'To receive alerts about your electricity usage, Lekky needs permission to send notifications. This allows the app to:',
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurface,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildInfoFeatureRow(context, Icons.battery_alert,
                        'Alert you when your balance is low'),
                    const SizedBox(height: 8),
                    _buildInfoFeatureRow(context, Icons.schedule,
                        'Remind you when it\'s time to top up'),
                    const SizedBox(height: 8),
                    _buildInfoFeatureRow(context, Icons.error_outline,
                        'Notify you about data validation issues'),
                    const SizedBox(height: 16),
                    Text(
                      'You can manage these settings at any time.',
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.colorScheme.onSurface.withOpacity(0.7),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Fixed button bar
            Padding(
              padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
              child: Row(
                children: [
                  const Spacer(),
                  LekkyButton(
                    text: 'OK',
                    type: LekkyButtonType.primary,
                    size: LekkyButtonSize.compact,
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build individual info feature row
  Widget _buildInfoFeatureRow(
      BuildContext context, IconData icon, String text) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.primary,
          size: 18,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  /// Update background monitoring based on current settings
  Future<void> updateBackgroundMonitoring() async {
    try {
      final backgroundService = BackgroundMonitoringService();
      await backgroundService.updateMonitoring();
    } catch (e) {
      Logger.error('Error updating background monitoring: $e');
    }
  }
}
